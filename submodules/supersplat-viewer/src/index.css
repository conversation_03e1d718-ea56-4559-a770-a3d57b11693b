* {
    margin: 0;
    padding: 0;
    touch-action: none;
}
body {
    overflow: hidden;
    font-family: 'Arial', sans-serif;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}
.hidden {
    display: none !important;
}
#ui {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}
#ui * {
    pointer-events: auto;
}
#infoPanel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
}
#infoPanel > #infoPanelContent {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translate(-50%, 0);
    min-height: 280px;
    min-width: 320px;
    padding: 8px;
    border-radius: 24px;
    color: black;
    background-color: #eeed;
    display: flex;
    flex-direction: column;
}
#infoPanel > #infoPanelContent > #header {
    font-size: 24px;
    font-weight: bold;
    padding-bottom: 16px;
    color: #444;
    display: none;
}
#infoPanel > #infoPanelContent > #tabs {
    display: flex;
    gap: 16px;
    background-color: #666;
    padding: 8px;
    border-radius: 22px;
}
#infoPanel > #infoPanelContent > #tabs > .tab {
    padding: 8px;
    border-radius: 16px;
    cursor: pointer;
    flex-grow: 1;
    text-align: center;
    font-weight: bold;
    font-size: 14px;
    color: #222;
    transition: background-color 250ms ease;
}
#infoPanel > #infoPanelContent > #tabs > .tab:hover {
    background-color: #eee;
}
#infoPanel > #infoPanelContent > #tabs > .tab.active {
    background-color: #eee;
}
#infoPanel > #infoPanelContent > #infoPanels {
    padding: 16px;
}
#infoPanel > #infoPanelContent .control-item {
    display: flex;
    justify-content: space-between;
    gap: 32px;
    line-height: 1.5;
}
#infoPanel > #infoPanelContent > #infoPanels h1 {
    font-size: 14px;
    font-weight: bold;
    padding: 0 0 6px 0;
}
#infoPanel > #infoPanelContent .control-item > .control-action {
    text-align: left;
    color: #333;
}
#infoPanel > #infoPanelContent .control-item > .control-key {
    text-align: right;
    color: #666;
}
#infoPanel > #infoPanelContent .control-spacer {
    border-bottom: 1px dashed #666;
    margin: 10px 0;
}

/* loadingWrap */

#loadingWrap {
    position: fixed;
    bottom: 120px;
    left: 50%;
    transform: translate(-50%, 0);
    width: 380px;

    display: flex;
    flex-direction: column;

    padding: 16px;
}
#loadingWrap > #loadingText {
    font-size: 18px;
    color: white;
    text-align: center;
    text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}
#loadingWrap > #loadingBar {
    width: 100%;
    height: 10px;
    margin-top: 8px;
    border-radius: 4px;
    overflow: hidden;
}

/* controlsWrap */

#controlsWrap {
    position: absolute;
    left: max(16px, env(safe-area-inset-left));
    right: max(16px, env(safe-area-inset-right));
    bottom: max(16px, env(safe-area-inset-bottom));

    display: flex;
    flex-direction: column;
}

#controlsWrap.faded-in {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}
#controlsWrap.faded-out {
    visibility: hidden;
    opacity: 0;
    transition: visibility 0s 0.5s, opacity 0.5s ease-out;
}

#controlsWrap > #timelineContainer {
    height: 30px;
    cursor: pointer;
}
#controlsWrap > #timelineContainer > #line {
    width: 100%;
    height: 50%;
    border-bottom: 1px solid #8888;
}
#controlsWrap > #timelineContainer > #handle {
    position: absolute;
    top: 15.5px;
    width: 10px;
    height: 10px;
    transform: translate(-50%, -50%);
    border: 1px solid black;
    border-radius: 50%;
    background-color: #fff8;
}
#controlsWrap > #timelineContainer > #time {
    position: absolute;
    top: 0;
    padding: 2px 4px;
    transform: translate(-50%, -100%);
    font-size: 12px;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
}
#controlsWrap > #buttonContainer {
    display: flex;
    gap: 8px;
}

/* spacer */

.spacer {
    flex-grow: 1;
}

/* settingsPanel */

#settingsPanel {
    position: fixed;
    right: max(16px, env(safe-area-inset-right));
    bottom: calc(max(16px, env(safe-area-inset-bottom)) + 70px);
    padding: 10px;
    color: #eee;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 8px;

    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;

    font-size: 14px;
}
#settingsPanel > .settingsRow {
    display: flex;
    gap: 4px;
    width: 100%;
    
}
#settingsPanel > .settingsRow > .button {
    padding: 10px 0px;
    flex-grow: 1;
}

/* toggleWrap */

.toggleWrap {
    position: relative;
    background-color: #dddd;
    border: 1px solid #888;
    border-radius: 8px;
    height: 40px;
    width: 120px;
}
.toggleWrap > .toggleHighlight {
    position: absolute;
    left: 0;
    width: 50%;
    height: 100%;
    background-color: #eee;
    border-radius: 8px;
    transition: left 0.1s ease-out;
}
.toggleWrap > .toggleHighlight.right {
    left: 60px;
}
.toggleWrap > button {
    position: absolute;
    width: 60px;
    height: 100%;
    border: 0;
    border-radius: 8px;

    cursor: pointer;
    font-size: 14px;
    letter-spacing: -0.25px;

    color: #222;
    background-color: transparent;

    appearance: none;
}
.toggleWrap > button:hover {
    color: black;
}
.toggleWrap > button.left {
    left: 0;
}
.toggleWrap > button.right {
    right: 0;
}

/* button */
.button {
    width: 40px;
    height: 40px;
    padding: 0;
    margin: 0;
    color: #222;
    background-color: #dddd;
    border: 1px solid #888;
    border-radius: 8px;
    cursor: pointer;

    /* font-weight: bold; */
    font-size: 14px;
    letter-spacing: -0.25px;
}
.button:hover {
    background-color: #eee;
}
.button > svg {
    display: block;
    margin: auto;
}
#poster {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
button {
    color: #222;
}
button:active {
    outline: none;
}
#joystickBase {
    position: absolute;
    width: 96px;
    height: 96px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    touch-action: none;

    background: radial-gradient(circle at center, #0000 50%, #000f 100%);
    background-color: #0002;
}

#joystickBase > #joystick {
    position: absolute;
    width: 48px;
    height: 48px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    touch-action: none;

    background-color: #fff8;
}