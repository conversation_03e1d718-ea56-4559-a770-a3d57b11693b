const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors({
    origin: 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization']
}));

// Handle preflight OPTIONS requests
app.options('*', (req, res) => {
    res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.sendStatus(200);
});

// Proxy middleware for API requests
const apiProxy = createProxyMiddleware({
    target: 'https://unicity3dev-api.bdnrc.org.cn',
    changeOrigin: true,
    pathRewrite: {
        '^/api': '', // Remove /api prefix when forwarding to target
    },
    onProxyReq: (proxyReq, req, res) => {
        console.log(`Proxying ${req.method} ${req.url} to https://unicity3dev-api.bdnrc.org.cn${req.url.replace('/api', '')}`);
    },
    onProxyRes: (proxyRes, req, res) => {
        // Override problematic CORS headers from the upstream server
        proxyRes.headers['access-control-allow-origin'] = 'http://localhost:3000';
        proxyRes.headers['access-control-allow-credentials'] = 'true';
        proxyRes.headers['access-control-allow-methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
        proxyRes.headers['access-control-allow-headers'] = 'Origin, X-Requested-With, Content-Type, Accept, Authorization';

        // Remove any duplicate or conflicting CORS headers
        delete proxyRes.headers['Access-Control-Allow-Origin'];
        delete proxyRes.headers['access-control-allow-origin'];

        // Set the correct single CORS header
        proxyRes.headers['access-control-allow-origin'] = 'http://localhost:3000';

        console.log(`Response headers set for ${req.url}`);
    },
    onError: (err, req, res) => {
        console.error('Proxy error:', err);
        res.status(500).json({ error: 'Proxy error', details: err.message });
    }
});

// Use the proxy for all /api routes
app.use('/api', apiProxy);

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', message: 'Proxy server is running' });
});

app.listen(PORT, () => {
    console.log(`Proxy server running on http://localhost:${PORT}`);
    console.log(`Proxying /api/* requests to https://unicity3dev-api.bdnrc.org.cn`);
    console.log(`Make sure your main app is running on http://localhost:3000`);
});
