import { ExperienceSettings } from './splat-serialize';
// 定义全局变量
let globalExperienceSettings: ExperienceSettings | null = null;

// 提供设置和获取全局变量的方法
const setGlobalExperienceSettings = (settings: ExperienceSettings) => {
    globalExperienceSettings = settings;
};

const getGlobalExperienceSettings = (): ExperienceSettings | null => {
    return globalExperienceSettings;
};


let projectId:string |null=null;
// 提供设置和获取全局变量的方法
const setProjectId = (projectid: string) => {
    projectId = projectid;
};

const getProjectId= (): string| null => {
    return projectId;
};

export { setGlobalExperienceSettings, getGlobalExperienceSettings ,setProjectId, getProjectId};