@use 'colors.scss' as *;

.settings-dialog {
    width: 100%;
    height: 100%;

    background-color: $bcg-darken;

    pointer-events: all;

    #dialog {
        position: absolute;
        width: 400px;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);

        display: flex;
        flex-direction: column;
        overflow: hidden;

        border-radius: 8px;
        background-color: $bcg-primary;

        filter: drop-shadow(5px 5px 10px rgba(0, 0, 0, 0.8));

        // the following is needed to get drop-shadow working on safari
        will-change: transform;

        #header {
            height: 32px;
            margin: 0;
            padding: 0px 12px;

            background-color: $bcg-darker;

            #icon {
                vertical-align: middle;
                color: $clr-icon-hilight;
            }

            #text {
                margin: 0;
                padding: 0px 12px;
                line-height: 32px;
                font-weight: bold;
                color: $text-primary;
            }
        }

        #content {
            min-height: 100px;
            padding: 12px;

            .row {
                // height: 24px;
                line-height: 24px;
                padding-bottom: 8px;

                &:not(.pcui-hidden) {
                    display: flex;
                }

                .label {
                    width: 140px;
                    margin: 0px;
                    flex-grow: 0;
                    flex-shrink: 0;
                }

                .select, .color-picker, .slider, .text-input, .boolean, .text-area {
                    margin: 0;
                    flex-grow: 1;
                }

                .boolean {
                    margin: 5px 0 0 0;
                    flex-grow: 0;
                }

                .slider .pcui-slider-bar {
                    margin-right: 0px;
                    width: calc(100% - 9px);
                }

                .vector-input {
                    margin: 0;
                    flex-grow: 1;

                    div.pcui-numeric-input {
                        margin-top: 0;
                        margin-bottom: 0;
                        line-height: 22px;
                        > .pcui-numeric-input-slider-control::after {
                            top: -6px;
                        }
                    }
                }
            }
        }

        #footer {
            display: flex;
            justify-content: center;
            padding-bottom: 4px;

            .button {
                width: 120px;
                height: 30px;
                border-radius: 4px;

                &:hover {
                    color: $text-primary;
                    background-color: $clr-hilight;
                }
            }
        }
    }
}
