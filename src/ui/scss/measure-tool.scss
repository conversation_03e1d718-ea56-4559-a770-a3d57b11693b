@use 'colors.scss' as *;

.measure-button {
    margin-left: 8px;

    &.active {
        background-color: $clr-hilight;
        color: $clr-active;
    }
}

.measure-panel {
    margin-top: 8px;
    padding: 8px;
    background-color: $bcg-primary;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.measure-label {
    font-size: 12px;
    color: $text-primary;
    margin-bottom: 4px;
}

.distance-input {
    width: 100%;
    background-color: $bcg-dark;
    color: $text-primary;
    border: 1px solid $bcg-darker;
    border-radius: 2px;
    padding: 4px;
    font-size: 12px;
}