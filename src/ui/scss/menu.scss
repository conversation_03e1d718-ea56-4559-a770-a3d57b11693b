@use 'colors.scss' as *;

#menu {
    position: absolute;
}

#menu-bar {
    transition: width 0.1s ease;

    position: absolute;
    top: 24px;
    left: 24px;
    height: 50px;
    border-radius: 8px;

    overflow: hidden;

    background-color: $bcg-primary;

    display: flex;
    flex-direction: row;
    align-items: center;
}

#app-icon {
    width: 54px;
    font-family: 'pc-icon';

    cursor: pointer;
}

#menu-arrow {
    display: none;
}

#menu-container {
    display: flex;
    flex-direction: column;
}

#menu-bar-options {
    display: flex;
    flex-direction: row;
    flex-grow: 1;
}

.menu-icon {
    width: 16px;
    height: 16px;
    padding: 16px 10px;
    margin: 0px;
    flex-grow: 1;
    color: $text-secondary;

    cursor: pointer;
    &:hover {
        color: $text-primary;
        background-color: $bcg-dark;
    }
}

.menu-option {
    padding: 16px 20px;
    margin: 0px;
    flex-grow: 1;
    text-align: center;
    text-overflow: clip;

    background-color: $bcg-primary;

    cursor: pointer;

    &:hover {
        color: $text-primary;
        background-color: $bcg-dark;
    }
}

.collapsed {
    #menu-bar {
        width: 90px;
    }

    #menu-collapse {
        display: none;
    }

    #menu-arrow {
        display: block;
    }

    .menu-option {
        display: none;
    }
}