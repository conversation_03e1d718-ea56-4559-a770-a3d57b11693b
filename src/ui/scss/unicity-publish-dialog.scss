@use 'colors.scss' as *;

#unicity-publish-dialog {
    width: 100%;
    height: 100%;

    background-color: $bcg-darken;

    pointer-events: all;

    #dialog {
        position: absolute;
        width: 400px; // 稍微宽一点，以适应更多内容
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);

        display: flex;
        flex-direction: column;
        overflow: hidden;

        border-radius: 8px;
        background-color: $bcg-primary;

        filter: drop-shadow(5px 5px 10px rgba(0, 0, 0, 0.8));

        // 以下是为了在Safari上使drop-shadow正常工作
        will-change: transform;

        #header {
            height: 32px;
            line-height: 32px;
            margin: 0px;
            padding: 0px 12px;

            font-weight: bold;
            color: $text-primary;
            background-color: $bcg-darker;

            #icon {
                vertical-align: middle;
                color: $clr-icon-hilight;
            }
        }

        #content {
            min-height: 100px;
            padding: 12px;
            max-height: 70vh; // 限制最大高度，避免在小屏幕上溢出
            overflow-y: auto; // 如果内容太多，添加滚动条

            .row {
                height: 24px;
                line-height: 24px;
                padding-bottom: 8px;

                &:not(.pcui-hidden) {
                    display: flex;
                }

                .label {
                    margin: 0px;
                    flex-grow: 1;
                }

                .label-right {
                    margin: 0px;
                    flex-grow: 1;
                    text-align: right;
                }

                .select, .color-picker, .slider {
                    margin: 0px;
                    width: 180px;
                }

                .text-input {
                    margin: 0px;
                    width: 180px;
                }

                // 为文本区域添加特殊样式
                .text-area {
                    margin: 0px;
                    width: 180px;
                    height: 60px; // 文本区域需要更多高度
                }
            }

            // 为包含文本区域的行添加特殊样式
            .row:has(.text-area) {
                height: 60px; // 调整行高以适应文本区域
                align-items: flex-start; // 顶部对齐
                padding-bottom: 12px; // 增加底部间距
            }
        }

        #footer {
            display: flex;
            justify-content: center;
            padding: 8px 0;

            .button {
                width: 140px; // 稍微宽一点，以适应"发布到Unicity"文本
                height: 30px;
                border-radius: 4px;
                margin: 0 8px; // 按钮之间添加间距

                &:hover {
                    color: $text-primary;
                    background-color: $clr-hilight;
                }
            }
        }
    }
}
