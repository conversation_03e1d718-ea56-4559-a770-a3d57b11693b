@use 'colors.scss' as *;

#data-panel {
    width: 100%;
    height: 320px;
}

#data-panel-popup-container {
    position: absolute;
    left: 50px;
    top: 50px;
}

#data-panel-popup-label {
    background-color: $bcg-dark;
    color: $text-primary;
}

#data-controls-container {
    width: 256px;
    flex-grow: 0;
    flex-shrink: 0;
    overflow-y: auto;
}

#data-controls {
    width: 100%;
}

.control-parent {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-grow: 1;
}

.control-label {
    width: 100px;
    flex-shrink: 0;
    flex-grow: 0;
    line-height: 32px;
    margin: 0px 6px 0px 6px;
}

.control-element {
    flex-shrink: 0;
    flex-grow: 0;
}

.control-element-expand {
    flex-grow: 1;
}

.control-element.pcui-boolean-input {
    margin-top: 10px;
}

#histogram-container {
    flex-grow: 1;
    flex-shrink: 1;
}

#histogram-canvas {
    image-rendering: pixelated;
}

#histogram-svg {
    pointer-events: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}
