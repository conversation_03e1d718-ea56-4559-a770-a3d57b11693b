@use 'colors.scss' as *;

.panel {
    position: absolute;
    border-radius: 8px;
    overflow: hidden;

    background-color: $bcg-primary;

    & > .panel-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 2px;

        background-color: $bcg-dark;

        & > .panel-header-icon {
            font-family: pc-icon;
            font-weight: bold;
            font-size: 13px;
            color: $clr-hilight;
        }

        & > .panel-header-label {
            color: $text-primary;
            font-weight: bold;
            flex-grow: 1;
        }
    }

    .panel-header-button {
        font-family: pc-icon;
        font-weight: bold;
        font-size: 13px;
        color: $clr-hilight;
    
        padding: 4px;
        flex-grow: 0;
        flex-shrink: 0;

        border-radius: 4px;

        text-align: center;

        svg {
            color: $clr-hilight;
        }
    
        &:hover {
            color: #ff9900;
            background-color: $bcg-darkest;
            cursor: pointer;
        }
    }

    .panel-header-spacer {
        flex-grow: 1;
        padding: 0px;
        margin: 0px;
    }
}