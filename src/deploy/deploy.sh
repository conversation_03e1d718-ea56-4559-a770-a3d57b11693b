
# 加载配置
source "$(dirname "$0")/config.sh"
# --------------------------
# 构建项目
# --------------------------
LocalBuild(){
    echo "构建项目..."
    npm run build ||{
    echo "❌ 构建失败"
    exit 1
    }
     echo "✅ 项目构建成功"
}
# --------------------------
# 创建远程目录
# --------------------------
CreateDir(){
    echo "创建远程目录..."
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        mkdir -p ${REMOTE_RELEASES}&&
        mkdir -p ${REMOTE_RELEASES}/${RELEASE_NAME}&&
        mkdir -p ${REMOTE_CURRENT}" ||{
            echo "❌ 创建远程目录失败"
            exit 1
        }
        echo "✅ 创建远程目录成功"
}
# --------------------------
# 上传文件
# --------------------------
UploadFiles(){
    echo "上传文件..."
    scp -r ./dist ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_RELEASES}/${RELEASE_NAME}/ ||{
         echo "❌ 上传 dist 文件失败"
         exit 1  
    }
    scp ./package.json ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_RELEASES}/${RELEASE_NAME}/ || {
        echo "❌ 上传 package.json 文件失败"
        exit 1
    }
    scp ./package-lock.json ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_RELEASES}/${RELEASE_NAME}/ || {
        echo "❌ 上传 package-lock.json 文件失败"
        exit 1
    }
    echo "✅ 文件上传完成"
}

# --------------------------
# 更新软链接
# --------------------------
UpdateSoftLink(){
    echo "更新软链接..."
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        find ${REMOTE_CURRENT} -type l -exec rm -f {} \; &&
        ln -sfn ${REMOTE_RELEASES}/${RELEASE_NAME} ${REMOTE_CURRENT} 
  " ||{
         echo "❌ 更新软链接失败"
        exit 1
     }
    echo "✅ 软链接更新完成"
}
# --------------------------
# 删除旧版本，仅在Releases文件夹保留最近的 5 个版本
# --------------------------
ClearReleaseVersion(){
    echo "清理旧版本..."
     MAX_TO_KEEP=$((MAXRELEASES + 1)) # 本地计算
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        cd ${REMOTE_RELEASES} &&
        ls -t | tail -n +$((MAX_TO_KEEP)) | xargs rm -rf
    " ||{
             echo "❌ 清理旧版本失败"
         exit 1
    }
    echo "✅ 清理旧版本完成"
}

# --------------------------
# 更新 Nginx 配置
# --------------------------
 UpdateNginxConfig() {
    echo "更新 Nginx 配置..."
    # 定义 Nginx 配置内容
    NGINX_CONFIG_CONTENT=$(cat <<EOF
    server {
        listen 8080;
        server_name ${SITE_NAME};
        root ${REMOTE_CURRENT}/${RELEASE_NAME}/dist;
        index index.html;
        location / {
             try_files \$uri \$uri/ /index.html;
        }
    }
EOF
    )
     # 备份原配置
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        echo ${SUDO_PASSWORD} | sudo -S cp /etc/nginx/sites-available/${SITE_NAME} /etc/nginx/sites-available/${SITE_NAME}.backup-\$(date +%Y%m%d_%H%M%S) || true
    "
     # 将新配置写入临时文件并上传到服务器
    echo "${NGINX_CONFIG_CONTENT}" > nginx.conf
    scp nginx.conf ${REMOTE_USER}@${REMOTE_HOST}:/tmp/${SITE_NAME}.conf
    rm -f nginx.conf

 # 移动配置文件到 Nginx 配置目录并重新加载 Nginx
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        echo ${SUDO_PASSWORD} | sudo -S mv /tmp/${SITE_NAME}.conf /etc/nginx/sites-available/${SITE_NAME} &&
        echo ${SUDO_PASSWORD} | sudo -S nginx -t " || {
        echo "❌ Nginx 配置验证失败"
        exit 1
      }

     # 如果验证通过，则重新加载 Nginx
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        echo ${SUDO_PASSWORD} | sudo -S systemctl reload nginx
    " || {
        echo "❌ Nginx 重启失败,正在启动 Ngix"
    }
    # 如果验证通过，则启动 Nginx
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        echo ${SUDO_PASSWORD} | sudo -S systemctl start nginx
    " || {
        echo "❌ Nginx 启动失败"
        exit 1
    }
    echo "✅ Nginx 配置更新完成"
}

LocalBuild
CreateDir
UploadFiles
UpdateSoftLink
ClearReleaseVersion
UpdateNginxConfig
echo "部署完成！"