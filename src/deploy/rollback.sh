# 加载配置
source "$(dirname "$0")/config.sh"

# --------------------------
# 回滚到指定版本
# --------------------------
Rollback() {
    echo "开始回滚版本..."

    # 获取远程服务器上的所有版本
    echo "获取可用版本列表..."
    AVAILABLE_RELEASES=$(ssh ${REMOTE_USER}@${REMOTE_HOST} "
        cd ${REMOTE_RELEASES} &&
        ls -t
    ")

    if [ -z "${AVAILABLE_RELEASES}" ]; then
        echo "❌ 没有找到任何版本，无法回滚"
        exit 1
    fi

    echo "以下是可用的版本："
    echo "${AVAILABLE_RELEASES}" | nl

    # 提示用户选择版本
    echo -n "请输入要回滚的版本编号（例如 1 表示最新版本）："
    read VERSION_INDEX

    # 获取用户选择的版本
    SELECTED_RELEASE=$(echo "${AVAILABLE_RELEASES}" | sed -n "${VERSION_INDEX}p")

    if [ -z "${SELECTED_RELEASE}" ]; then
        echo "❌ 无效的版本编号，无法回滚"
        exit 1
    fi

    echo "回滚到版本: ${SELECTED_RELEASE}"

    # 更新软链接到指定版本
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        find ${REMOTE_CURRENT} -type l -exec rm -f {} \; &&
        ln -sfn ${REMOTE_RELEASES}/${SELECTED_RELEASE} ${REMOTE_CURRENT}
    " || {
        echo "❌ 更新软链接失败"
        exit 1
    }

    echo "✅ 软链接已更新到版本: ${SELECTED_RELEASE}"
}
# --------------------------
# 更新 Nginx 配置
# --------------------------
UpdateNginxConfig() {
    SELECTED_RELEASE=$1
    echo "更新 Nginx 配置..."

    # 定义 Nginx 配置内容
    NGINX_CONFIG_CONTENT=$(cat <<EOF
server {
    listen 8080;
    server_name ${SITE_NAME};
    root ${REMOTE_CURRENT}/${SELECTED_RELEASE}/dist;
    index index.html;
    location / {
        try_files \$uri \$uri/ /index.html;
    }
}
EOF
    )

    # 备份原配置
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        echo ${SUDO_PASSWORD} | sudo -S cp /etc/nginx/sites-available/${SITE_NAME} /etc/nginx/sites-available/${SITE_NAME}.backup-\$(date +%Y%m%d_%H%M%S) || true
    "

    # 将新配置写入临时文件并上传到服务器
    echo "${NGINX_CONFIG_CONTENT}" > nginx.conf
    scp nginx.conf ${REMOTE_USER}@${REMOTE_HOST}:/tmp/${SITE_NAME}.conf
    rm -f nginx.conf

    # 移动配置文件到 Nginx 配置目录并验证配置
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        echo ${SUDO_PASSWORD} | sudo -S mv /tmp/${SITE_NAME}.conf /etc/nginx/sites-available/${SITE_NAME} &&
        echo ${SUDO_PASSWORD} | sudo -S nginx -t
    " || {
        echo "❌ Nginx 配置验证失败"
        exit 1
    }

    # 如果验证通过，则重新加载 Nginx
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        echo ${SUDO_PASSWORD} | sudo -S systemctl reload nginx
    " || {
        echo "❌ Nginx 重启失败,正在启动 Ngix"
    }
    # 如果验证通过，则启动 Nginx
    ssh ${REMOTE_USER}@${REMOTE_HOST} "
        echo ${SUDO_PASSWORD} | sudo -S systemctl start nginx
    " || {
        echo "❌ Nginx 启动失败"
        exit 1
    }
    echo "✅ Nginx 配置更新完成"
}

# 调用 Rollback 方法
Rollback
# 调用 UpdateNginxConfig 方法更新 Nginx 配置
UpdateNginxConfig "${SELECTED_RELEASE}"
echo "✅ 服务已重启，回滚完成"