import { Events } from './events';
import { getUnicityUser, UnicityUser, UnicityPublishSettings } from './unicity-service';
import { setProjectId, getGlobalExperienceSettings } from './sceneGlobalSetting';

/**
 * 注册Unicity相关的事件
 */
const registerUnicityEvents = (events: Events) => {
    /**
     * 检查Unicity功能是否启用
     * 目前简单地检查用户是否已登录
     */
    events.function('unicity.enabled', async () => {
        return !!(await getUnicityUser());
    });

    /**
     * 获取当前登录的Unicity用户
     */
    events.function('unicity.getUser', async (): Promise<UnicityUser | null> => {
        return await getUnicityUser();
    });

    let publishSettings: UnicityPublishSettings;

    events.function('unicity.getPublishSettings', async(): Promise<UnicityPublishSettings | null> => {
        if (!publishSettings) {
            const experienceSettings = getGlobalExperienceSettings();
            const viewBands = events.invoke('view.bands');
            publishSettings = {
                projectId: '5',
                serializeSettings: {
                    maxSHBands: viewBands,
                    minOpacity: 1 / 255,
                    removeInvalid: true
                },
                experienceSettings: experienceSettings
            };
        }

        return publishSettings;
    })

    events.function('unicity.setPublishSettings', async (settings: UnicityPublishSettings) => {
        publishSettings = settings;
    });
     /**
     * TODO：获取本次项目Id
     */
   
    events.function('unicity.getProjectId', async (): Promise<string | null> => {
        // 从 URL 中获取 projectId 参数
        const url = new URL(location.href);
        const projectId= url.searchParams.get('projectId');
        setProjectId(projectId)
        return projectId; // 返回 projectId，如果没有则返回 null
    });
};

export { registerUnicityEvents };
