<!DOCTYPE html>
<html>
    <head>
        <title>SuperSplat</title>
        <meta charset="utf-8" />
        <base href="__BASE_HREF__">
        <link rel="manifest" href="./manifest.json">
        <link rel="stylesheet" href="./index.css">
        <link rel="shortcut icon" href="#">
        <meta name="description" content="SuperSplat is an advanced browser-based editor for manipulating and optimizing 3D Gaussian Splats. It is open source and engine agnostic." />
        <meta name="keywords" content="PL<PERSON>, Gaussian, <PERSON>p<PERSON>, PlayCanvas, Editor" />
        <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

        <!-- Open Graph / Facebook -->
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://playcanvas.com/supersplat/editor" />
        <meta property="og:title" content="SuperSplat" />
        <meta property="og:description" content="SuperSplat is an advanced browser-based editor for manipulating and optimizing 3D Gaussian Splats. It is open source and engine agnostic." />
        <meta property="og:image" content="https://playcanvas.com/supersplat/editor/static/images/header.webp" />

        <!-- Twitter -->
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://playcanvas.com/supersplat/editor" />
        <meta property="twitter:title" content="SuperSplat" />
        <meta property="twitter:description" content="SuperSplat is an advanced browser-based editor for manipulating and optimizing 3D Gaussian Splats. It is open source and engine agnostic." />
        <meta property="twitter:image" content="https://playcanvas.com/supersplat/editor/static/images/header.webp" />

        <!-- jszip -->
        <script src="jszip.js"></script>

        <!-- Service worker -->
        <script>
            const sw = navigator.serviceWorker;
            if (sw) {
                sw.register('./sw.js')
                    .then(reg => console.log('service worker registered', reg))
                    .catch(err => console.log('failed to register service worker', err));
            }
        </script>
    </head>

    <body>
        <script type="module" src="./index.js"></script>
    </body>
</html>
